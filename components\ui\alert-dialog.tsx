import * as React from "react"
import { createPortal } from "react-dom"
import { X } from "lucide-react"

interface AlertDialogContextType {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const AlertDialogContext = React.createContext<AlertDialogContextType | undefined>(undefined)

const useAlertDialog = () => {
  const context = React.useContext(AlertDialogContext)
  if (!context) {
    throw new Error("AlertDialog components must be used within an AlertDialog")
  }
  return context
}

interface AlertDialogProps {
  children: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

const AlertDialog: React.FC<AlertDialogProps> = ({ children, open: controlledOpen, onOpenChange: controlledOnOpenChange }) => {
  const [internalOpen, setInternalOpen] = React.useState(false)

  const open = controlledOpen !== undefined ? controlledOpen : internalOpen
  const onOpenChange = controlledOnOpenChange || setInternalOpen

  return (
    <AlertDialogContext.Provider value={{ open, onOpenChange }}>
      {children}
    </AlertDialogContext.Provider>
  )
}

const AlertDialogTrigger: React.FC<{
  children: React.ReactNode
  asChild?: boolean
}> = ({ children, asChild = false }) => {
  const { onOpenChange } = useAlertDialog()

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children as React.ReactElement<any>, {
      onClick: () => onOpenChange(true)
    })
  }

  return (
    <button onClick={() => onOpenChange(true)}>
      {children}
    </button>
  )
}

interface AlertDialogContentProps {
  children: React.ReactNode
  className?: string
}

const AlertDialogContent: React.FC<AlertDialogContentProps> = ({ children, className = "" }) => {
  const { open, onOpenChange } = useAlertDialog()

  if (!open) return null

  const dialogElement = (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      <div
        className="fixed inset-0 bg-black/50"
        onClick={() => onOpenChange(false)}
      />
      <div className={`relative bg-white rounded-lg shadow-lg max-w-lg w-full max-h-[90vh] overflow-y-auto ${className}`}>
        {children}
      </div>
    </div>
  )

  // Use portal to render dialog at document root
  if (typeof document !== 'undefined') {
    return createPortal(dialogElement, document.body)
  }

  return dialogElement
}

const AlertDialogHeader: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ""
}) => (
  <div className={`flex flex-col space-y-2 text-center sm:text-left p-6 pb-0 ${className}`}>
    {children}
  </div>
)

const AlertDialogTitle: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ""
}) => (
  <h2 className={`text-lg font-semibold ${className}`}>
    {children}
  </h2>
)

const AlertDialogDescription: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ""
}) => (
  <p className={`text-sm text-gray-500 ${className}`}>
    {children}
  </p>
)

const AlertDialogFooter: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ""
}) => (
  <div className={`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-0 ${className}`}>
    {children}
  </div>
)

const AlertDialogAction: React.FC<{
  children: React.ReactNode
  onClick?: () => void
  className?: string
}> = ({ children, onClick, className = "" }) => {
  const { onOpenChange } = useAlertDialog()

  const handleClick = () => {
    onClick?.()
    onOpenChange(false)
  }

  return (
    <button
      onClick={handleClick}
      className={`
        inline-flex h-10 items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm
        font-semibold text-white ring-offset-white transition-colors hover:bg-red-700
        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-red-500
        focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
        ${className}
      `}
    >
      {children}
    </button>
  )
}

const AlertDialogCancel: React.FC<{
  children: React.ReactNode
  className?: string
}> = ({ children, className = "" }) => {
  const { onOpenChange } = useAlertDialog()

  return (
    <button
      onClick={() => onOpenChange(false)}
      className={`
        inline-flex h-10 items-center justify-center rounded-md border border-gray-300
        bg-white px-4 py-2 text-sm font-semibold ring-offset-white transition-colors
        hover:bg-gray-50 focus-visible:outline-none focus-visible:ring-2
        focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none
        disabled:opacity-50 mt-2 sm:mt-0
        ${className}
      `}
    >
      {children}
    </button>
  )
}

export {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
}
